# 测试简化后的文本分析代码
import subprocess
import sys

def install_package(package):
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✓ {package} 安装成功")
        return True
    except:
        print(f"✗ {package} 安装失败")
        return False

# 安装基础包
packages = [
    'pandas', 'numpy', 'matplotlib', 'seaborn', 
    'jieba', 'snownlp', 'scikit-learn', 'wordcloud', 'pillow'
]

print("开始安装依赖包...")
success_count = 0
for package in packages:
    if install_package(package):
        success_count += 1

print(f"\n安装完成！成功安装 {success_count}/{len(packages)} 个包")

if success_count == len(packages):
    print("所有依赖包安装成功，可以运行分析代码了！")
    
    # 测试导入
    try:
        import pandas as pd
        import numpy as np
        import matplotlib.pyplot as plt
        import jieba
        from snownlp import SnowNLP
        from sklearn.feature_extraction.text import TfidfVectorizer
        print("✓ 所有库导入成功")
        
        # 测试数据读取
        try:
            df = pd.read_csv('解忧杂货铺书籍评论数据.csv', encoding='gb18030', nrows=5)
            print(f"✓ 数据文件读取成功，包含 {len(df)} 行数据")
            print("数据列名:", df.columns.tolist())
        except Exception as e:
            print(f"✗ 数据文件读取失败: {e}")
            
    except Exception as e:
        print(f"✗ 库导入失败: {e}")
else:
    print("部分依赖包安装失败，请手动安装缺失的包")
