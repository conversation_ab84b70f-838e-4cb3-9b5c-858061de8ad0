{
 "cells": [
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "# 《解忧杂货铺》书籍评论多模态文本分析\n",
    "\n",
    "## 项目概述\n",
    "本项目对《解忧杂货铺》书籍评论数据进行全面的文本分析，包括情感分析、词云生成、预测建模、文本聚类、主题建模和社交网络分析。\n",
    "\n",
    "## 技术栈\n",
    "- 文本处理：jieba、SnowNLP\n",
    "- 机器学习：scikit-learn\n",
    "- 可视化：matplotlib、wordcloud、pyLDAvis\n",
    "- 网络分析：networkx\n",
    "- 数据处理：pandas、numpy"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# 安装必要的库\n",
    "import subprocess\n",
    "import sys\n",
    "\n",
    "def install_package(package):\n",
    "    try:\n",
    "        subprocess.check_call([sys.executable, \"-m\", \"pip\", \"install\", package])\n",
    "        print(f\"✓ {package} 安装成功\")\n",
    "    except:\n",
    "        print(f\"✗ {package} 安装失败\")\n",
    "\n",
    "# 安装所需包\n",
    "packages = [\n",
    "    'pandas', 'numpy', 'matplotlib', 'seaborn', \n",
    "    'jieba', 'snownlp', 'scikit-learn', 'wordcloud', 'pillow'\n",
    "]\n",
    "\n",
    "print(\"开始安装依赖包...\")\n",
    "for package in packages:\n",
    "    install_package(package)\n",
    "print(\"依赖包安装完成！\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# 导入必要的库\n",
    "import pandas as pd\n",
    "import numpy as np\n",
    "import matplotlib.pyplot as plt\n",
    "import seaborn as sns\n",
    "import jieba\n",
    "from snownlp import SnowNLP\n",
    "from wordcloud import WordCloud\n",
    "import re\n",
    "from collections import Counter\n",
    "import warnings\n",
    "warnings.filterwarnings('ignore')\n",
    "\n",
    "# 设置中文字体\n",
    "plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']\n",
    "plt.rcParams['axes.unicode_minus'] = False\n",
    "\n",
    "# 设置图片保存参数\n",
    "plt.rcParams['savefig.dpi'] = 300\n",
    "plt.rcParams['figure.dpi'] = 100"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 1. 数据加载与预处理"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# 加载数据\n",
    "df = pd.read_csv('解忧杂货铺书籍评论数据.csv', encoding='gb18030')\n",
    "print(f\"数据集大小: {df.shape}\")\n",
    "print(f\"列名: {df.columns.tolist()}\")\n",
    "print(\"\\n前5行数据:\")\n",
    "print(df.head())\n",
    "\n",
    "# 数据基本信息\n",
    "print(\"\\n数据基本信息:\")\n",
    "print(df.info())\n",
    "print(\"\\n缺失值统计:\")\n",
    "print(df.isnull().sum())"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# 数据清洗\n",
    "# 去除缺失值\n",
    "df = df.dropna(subset=['评论内容'])\n",
    "\n",
    "# 去除重复评论\n",
    "df = df.drop_duplicates(subset=['评论内容'])\n",
    "\n",
    "# 重置索引\n",
    "df = df.reset_index(drop=True)\n",
    "\n",
    "print(f\"清洗后数据集大小: {df.shape}\")\n",
    "print(f\"评分分布:\")\n",
    "print(df['用户评分'].value_counts())"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# 自定义词典 - 添加书籍专有名词\n",
    "custom_words = [\n",
    "    '浪矢杂货店', '解忧杂货铺', '时空穿越', '东野圭吾', \n",
    "    '浪矢爷爷', '杂货店', '咨询信', '回信',\n",
    "    '温暖', '治愈', '人生选择', '成长', '感动',\n",
    "    '青春', '梦想', '坚持', '勇气', '希望',\n",
    "    '家庭', '友情', '爱情', '亲情', '责任'\n",
    "]\n",
    "\n",
    "# 添加自定义词汇到jieba词典\n",
    "for word in custom_words:\n",
    "    jieba.add_word(word)\n",
    "\n",
    "print(\"自定义词典已加载\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# 中文停用词\n",
    "stop_words = {\n",
    "    '的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一个', '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没有', '看', '好', '自己', '这', '那', '他', '她', '它', '们', '这个', '那个', '什么', '怎么', '为什么', '因为', '所以', '但是', '然后', '还是', '或者', '如果', '虽然', '虽说', '不过', '只是', '只要', '就是', '还有', '以及', '以后', '以前', '现在', '当时', '时候', '地方', '方面', '问题', '工作', '可以', '应该', '能够', '需要', '希望', '觉得', '认为', '知道', '发现', '变成', '成为', '进行', '开始', '继续', '结束', '完成', '实现', '达到', '得到', '拿到', '找到', '遇到', '碰到', '看到', '听到', '想到', '做到', '用到', '说到', '谈到', '提到', '关于', '对于', '由于', '通过', '根据', '按照', '依据', '基于', '出于', '为了', '除了', '包括', '其中', '之间', '之后', '之前', '以来', '以下', '以上', '左右', '前后', '内外', '上下', '东西', '南北', '中间', '周围', '附近', '旁边', '对面', '里面', '外面', '上面', '下面', '前面', '后面', '这里', '那里', '哪里', '某些', '一些', '许多', '大量', '少量', '全部', '部分', '整个', '各种', '不同', '相同', '类似', '特别', '尤其', '特殊', '普通', '一般', '通常', '经常', '总是', '从来', '曾经', '已经', '正在', '将要', '可能', '也许', '大概', '估计', '差不多', '几乎', '完全', '非常', '十分', '相当', '比较', '更加', '最', '极其', '太', '很多', '不少', '一点', '一些', '有些', '这些', '那些', '哪些'\n",
    "}\n",
    "\n",
    "print(f\"停用词数量: {len(stop_words)}\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# 文本预处理函数\n",
    "def preprocess_text(text):\n",
    "    \"\"\"文本预处理：清洗、分词、去停用词\"\"\"\n",
    "    if pd.isna(text):\n",
    "        return []\n",
    "    \n",
    "    # 去除标点符号和特殊字符\n",
    "    text = re.sub(r'[^\\u4e00-\\u9fa5a-zA-Z0-9]', ' ', str(text))\n",
    "    \n",
    "    # 分词\n",
    "    words = jieba.lcut(text, cut_all=False)  # 精确模式\n",
    "    \n",
    "    # 去除停用词和长度小于2的词\n",
    "    words = [word.strip() for word in words \n",
    "             if word.strip() not in stop_words \n",
    "             and len(word.strip()) >= 2 \n",
    "             and word.strip().isalpha()]\n",
    "    \n",
    "    return words\n",
    "\n",
    "# 应用文本预处理\n",
    "print(\"开始文本预处理...\")\n",
    "df['processed_words'] = df['评论内容'].apply(preprocess_text)\n",
    "df['processed_text'] = df['processed_words'].apply(lambda x: ' '.join(x))\n",
    "\n",
    "print(\"文本预处理完成\")\n",
    "print(\"\\n预处理示例:\")\n",
    "for i in range(3):\n",
    "    print(f\"原文: {df.iloc[i]['评论内容'][:50]}...\")\n",
    "    print(f\"处理后: {df.iloc[i]['processed_text'][:50]}...\")\n",
    "    print()"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 2. 情感倾向分析"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# 使用SnowNLP进行情感分析\n",
    "def get_sentiment_score(text):\n",
    "    \"\"\"获取情感评分 (0-1, 越接近1越积极)\"\"\"\n",
    "    try:\n",
    "        s = SnowNLP(str(text))\n",
    "        return s.sentiments\n",
    "    except:\n",
    "        return 0.5  # 中性\n",
    "\n",
    "print(\"开始情感分析...\")\n",
    "df['sentiment_score'] = df['评论内容'].apply(get_sentiment_score)\n",
    "\n",
    "# 情感分类\n",
    "def classify_sentiment(score):\n",
    "    if score >= 0.6:\n",
    "        return '积极'\n",
    "    elif score <= 0.4:\n",
    "        return '消极'\n",
    "    else:\n",
    "        return '中性'\n",
    "\n",
    "df['sentiment_label'] = df['sentiment_score'].apply(classify_sentiment)\n",
    "\n",
    "print(\"情感分析完成\")\n",
    "print(\"\\n情感分布统计:\")\n",
    "sentiment_counts = df['sentiment_label'].value_counts()\n",
    "print(sentiment_counts)\n",
    "print(f\"\\n平均情感评分: {df['sentiment_score'].mean():.3f}\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# 情感分布可视化\n",
    "fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))\n",
    "\n",
    "# 情感分布直方图\n",
    "ax1.hist(df['sentiment_score'], bins=30, alpha=0.7, color='skyblue', edgecolor='black')\n",
    "ax1.set_xlabel('情感评分')\n",
    "ax1.set_ylabel('频次')\n",
    "ax1.set_title('情感评分分布直方图')\n",
    "ax1.axvline(df['sentiment_score'].mean(), color='red', linestyle='--', \n",
    "           label=f'平均值: {df[\"sentiment_score\"].mean():.3f}')\n",
    "ax1.legend()\n",
    "ax1.grid(True, alpha=0.3)\n",
    "\n",
    "# 情感分类饼图\n",
    "colors = ['#ff9999', '#66b3ff', '#99ff99']\n",
    "sentiment_counts = df['sentiment_label'].value_counts()\n",
    "wedges, texts, autotexts = ax2.pie(sentiment_counts.values, \n",
    "                                  labels=sentiment_counts.index,\n",
    "                                  autopct='%1.1f%%',\n",
    "                                  colors=colors,\n",
    "                                  startangle=90)\n",
    "ax2.set_title('情感分类占比饼图')\n",
    "\n",
    "# 美化饼图文字\n",
    "for autotext in autotexts:\n",
    "    autotext.set_color('white')\n",
    "    autotext.set_fontweight('bold')\n",
    "\n",
    "plt.tight_layout()\n",
    "plt.savefig('情感分析结果.png', dpi=300, bbox_inches='tight')\n",
    "plt.show()\n",
    "\n",
    "print(\"情感分析图表已保存为 '情感分析结果.png'\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 3. 高频词与词云分析"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# TF-IDF分析\n",
    "from sklearn.feature_extraction.text import TfidfVectorizer\n",
    "\n",
    "# 创建TF-IDF向量化器\n",
    "tfidf = TfidfVectorizer(max_features=2000, ngram_range=(1, 2))\n",
    "\n",
    "# 过滤空文本\n",
    "valid_texts = df[df['processed_text'].str.len() > 0]['processed_text'].tolist()\n",
    "print(f\"有效文本数量: {len(valid_texts)}\")\n",
    "\n",
    "# 计算TF-IDF\n",
    "tfidf_matrix = tfidf.fit_transform(valid_texts)\n",
    "feature_names = tfidf.get_feature_names_out()\n",
    "\n",
    "# 获取平均TF-IDF分数\n",
    "mean_scores = np.mean(tfidf_matrix.toarray(), axis=0)\n",
    "tfidf_scores = list(zip(feature_names, mean_scores))\n",
    "tfidf_scores.sort(key=lambda x: x[1], reverse=True)\n",
    "\n",
    "print(\"\\nTF-IDF权重最高的50个词:\")\n",
    "top_50_words = tfidf_scores[:50]\n",
    "for i, (word, score) in enumerate(top_50_words, 1):\n",
    "    print(f\"{i:2d}. {word:10s} {score:.4f}\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# 词频统计\n",
    "all_words = []\n",
    "for words in df['processed_words']:\n",
    "    all_words.extend(words)\n",
    "\n",
    "word_freq = Counter(all_words)\n",
    "print(f\"\\n总词汇数: {len(all_words)}\")\n",
    "print(f\"唯一词汇数: {len(word_freq)}\")\n",
    "\n",
    "print(\"\\n词频统计前30:\")\n",
    "for word, freq in word_freq.most_common(30):\n",
    "    print(f\"{word:10s} {freq:4d}\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# 生成词云图\n",
    "from PIL import Image\n",
    "\n",
    "# 创建书本形状的mask（简化版）\n",
    "def create_book_mask():\n",
    "    \"\"\"创建书本形状的遮罩\"\"\"\n",
    "    mask = np.zeros((400, 600))\n",
    "    # 书本主体\n",
    "    mask[50:350, 100:500] = 255\n",
    "    # 书脊\n",
    "    mask[50:350, 80:100] = 255\n",
    "    return mask\n",
    "\n",
    "book_mask = create_book_mask()\n",
    "\n",
    "# 准备词云文本\n",
    "wordcloud_text = ' '.join(all_words)\n",
    "\n",
    "# 生成词云\n",
    "wordcloud = WordCloud(\n",
    "    font_path='C:/Windows/Fonts/simhei.ttf',  # 中文字体路径\n",
    "    width=800, height=600,\n",
    "    background_color='white',\n",
    "    mask=book_mask,\n",
    "    max_words=200,\n",
    "    colormap='viridis',\n",
    "    relative_scaling=0.5,\n",
    "    random_state=42\n",
    ").generate(wordcloud_text)\n",
    "\n",
    "# 显示词云\n",
    "plt.figure(figsize=(12, 8))\n",
    "plt.imshow(wordcloud, interpolation='bilinear')\n",
    "plt.axis('off')\n",
    "plt.title('《解忧杂货铺》评论词云图（书本形状）', fontsize=16, pad=20)\n",
    "plt.tight_layout()\n",
    "plt.savefig('词云图.png', dpi=300, bbox_inches='tight')\n",
    "plt.show()\n",
    "\n",
    "print(\"词云图已保存为 '词云图.png'\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# 核心关键词分析\n",
    "core_keywords = ['温暖', '治愈', '人生选择', '感动', '东野圭吾', '杂货店', '时空', '穿越']\n",
    "\n",
    "print(\"核心关键词频次统计:\")\n",
    "keyword_freq = {}\n",
    "for keyword in core_keywords:\n",
    "    count = sum(1 for text in df['评论内容'] if keyword in str(text))\n",
    "    keyword_freq[keyword] = count\n",
    "    print(f\"{keyword:10s}: {count:4d} 次\")\n",
    "\n",
    "# 可视化核心关键词频次\n",
    "plt.figure(figsize=(10, 6))\n",
    "keywords = list(keyword_freq.keys())\n",
    "frequencies = list(keyword_freq.values())\n",
    "\n",
    "bars = plt.bar(keywords, frequencies, color='lightcoral', alpha=0.8)\n",
    "plt.xlabel('关键词')\n",
    "plt.ylabel('出现频次')\n",
    "plt.title('核心关键词频次分布')\n",
    "plt.xticks(rotation=45)\n",
    "\n",
    "# 添加数值标签\n",
    "for bar, freq in zip(bars, frequencies):\n",
    "    plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 1, \n",
    "             str(freq), ha='center', va='bottom')\n",
    "\n",
    "plt.tight_layout()\n",
    "plt.savefig('核心关键词频次.png', dpi=300, bbox_inches='tight')\n",
    "plt.show()\n",
    "\n",
    "print(\"核心关键词频次图已保存为 '核心关键词频次.png'\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 4. 预测建模"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# 导入机器学习库\n",
    "from sklearn.model_selection import train_test_split\n",
    "from sklearn.tree import DecisionTreeClassifier\n",
    "from sklearn.naive_bayes import MultinomialNB\n",
    "from sklearn.svm import SVC\n",
    "from sklearn.metrics import accuracy_score, f1_score, classification_report, confusion_matrix\n",
    "from sklearn.preprocessing import LabelEncoder\n",
    "import seaborn as sns\n",
    "\n",
    "# 准备标签数据 - 基于情感分类进行预测\n",
    "# 过滤有效数据\n",
    "valid_data = df[df['processed_text'].str.len() > 0].copy()\n",
    "print(f\"用于建模的数据量: {len(valid_data)}\")\n",
    "\n",
    "# 特征工程：TF-IDF向量化\n",
    "tfidf_model = TfidfVectorizer(max_features=2000, ngram_range=(1, 2))\n",
    "X = tfidf_model.fit_transform(valid_data['processed_text'])\n",
    "y = valid_data['sentiment_label']\n",
    "\n",
    "print(f\"特征矩阵形状: {X.shape}\")\n",
    "print(f\"标签分布: {y.value_counts()}\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# 划分训练集和测试集\n",
    "X_train, X_test, y_train, y_test = train_test_split(\n",
    "    X, y, test_size=0.3, random_state=42, stratify=y\n",
    ")\n",
    "\n",
    "print(f\"训练集大小: {X_train.shape[0]}\")\n",
    "print(f\"测试集大小: {X_test.shape[0]}\")\n",
    "print(f\"训练集标签分布: {pd.Series(y_train).value_counts()}\")\n",
    "print(f\"测试集标签分布: {pd.Series(y_test).value_counts()}\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# 模型训练和评估\n",
    "models = {\n",
    "    '决策树': DecisionTreeClassifier(max_depth=5, criterion='entropy', random_state=42),\n",
    "    '朴素贝叶斯': MultinomialNB(alpha=1.0),\n",
    "    'SVM': SVC(kernel='linear', C=1, random_state=42)\n",
    "}\n",
    "\n",
    "results = {}\n",
    "\n",
    "for name, model in models.items():\n",
    "    print(f\"\\n训练 {name} 模型...\")\n",
    "    \n",
    "    # 训练模型\n",
    "    model.fit(X_train, y_train)\n",
    "    \n",
    "    # 预测\n",
    "    y_pred = model.predict(X_test)\n",
    "    \n",
    "    # 评估\n",
    "    accuracy = accuracy_score(y_test, y_pred)\n",
    "    f1 = f1_score(y_test, y_pred, average='weighted')\n",
    "    \n",
    "    results[name] = {\n",
    "        'model': model,\n",
    "        'accuracy': accuracy,\n",
    "        'f1_score': f1,\n",
    "        'y_pred': y_pred\n",
    "    }\n",
    "    \n",
    "    print(f\"{name} - 准确率: {accuracy:.4f}, F1-score: {f1:.4f}\")\n",
    "    print(f\"分类报告:\")\n",
    "    print(classification_report(y_test, y_pred))"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# 模型性能对比可视化\n",
    "model_names = list(results.keys())\n",
    "accuracies = [results[name]['accuracy'] for name in model_names]\n",
    "f1_scores = [results[name]['f1_score'] for name in model_names]\n",
    "\n",
    "fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))\n",
    "\n",
    "# 准确率对比\n",
    "bars1 = ax1.bar(model_names, accuracies, color=['skyblue', 'lightgreen', 'lightcoral'])\n",
    "ax1.set_ylabel('准确率')\n",
    "ax1.set_title('模型准确率对比')\n",
    "ax1.set_ylim(0, 1)\n",
    "for bar, acc in zip(bars1, accuracies):\n",
    "    ax1.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01, \n",
    "             f'{acc:.3f}', ha='center', va='bottom')\n",
    "\n",
    "# F1-score对比\n",
    "bars2 = ax2.bar(model_names, f1_scores, color=['skyblue', 'lightgreen', 'lightcoral'])\n",
    "ax2.set_ylabel('F1-Score')\n",
    "ax2.set_title('模型F1-Score对比')\n",
    "ax2.set_ylim(0, 1)\n",
    "for bar, f1 in zip(bars2, f1_scores):\n",
    "    ax2.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01, \n",
    "             f'{f1:.3f}', ha='center', va='bottom')\n",
    "\n",
    "plt.tight_layout()\n",
    "plt.savefig('模型性能对比.png', dpi=300, bbox_inches='tight')\n",
    "plt.show()\n",
    "\n",
    "print(\"模型性能对比图已保存为 '模型性能对比.png'\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# 混淆矩阵热力图\n",
    "fig, axes = plt.subplots(1, 3, figsize=(18, 5))\n",
    "\n",
    "for i, (name, result) in enumerate(results.items()):\n",
    "    cm = confusion_matrix(y_test, result['y_pred'])\n",
    "    \n",
    "    sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', \n",
    "                xticklabels=['消极', '积极', '中性'],\n",
    "                yticklabels=['消极', '积极', '中性'],\n",
    "                ax=axes[i])\n",
    "    \n",
    "    axes[i].set_title(f'{name}混淆矩阵')\n",
    "    axes[i].set_xlabel('预测标签')\n",
    "    axes[i].set_ylabel('真实标签')\n",
    "\n",
    "plt.tight_layout()\n",
    "plt.savefig('混淆矩阵热力图.png', dpi=300, bbox_inches='tight')\n",
    "plt.show()\n",
    "\n",
    "print(\"混淆矩阵热力图已保存为 '混淆矩阵热力图.png'\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 5. 文本聚类与LDA主题建模"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# 导入聚类和主题建模库\n",
    "from sklearn.cluster import KMeans\n",
    "from sklearn.decomposition import PCA, LatentDirichletAllocation\n",
    "from sklearn.feature_extraction.text import CountVectorizer\n",
    "import pyLDAvis\n",
    "import pyLDAvis.sklearn\n",
    "\n",
    "# K-means聚类\n",
    "print(\"开始K-means聚类分析...\")\n",
    "n_clusters = 3\n",
    "kmeans = KMeans(n_clusters=n_clusters, random_state=42, n_init=10)\n",
    "cluster_labels = kmeans.fit_predict(X)\n",
    "\n",
    "# 添加聚类标签到数据框\n",
    "valid_data['cluster'] = cluster_labels\n",
    "\n",
    "print(f\"聚类完成，共{n_clusters}个簇\")\n",
    "print(\"聚类分布:\")\n",
    "cluster_counts = pd.Series(cluster_labels).value_counts().sort_index()\n",
    "for i, count in enumerate(cluster_counts):\n",
    "    print(f\"簇 {i}: {count} 个样本\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# PCA降维可视化聚类结果\n",
    "print(\"进行PCA降维...\")\n",
    "pca = PCA(n_components=2, random_state=42)\n",
    "X_pca = pca.fit_transform(X.toarray())\n",
    "\n",
    "# 可视化聚类结果\n",
    "plt.figure(figsize=(12, 8))\n",
    "colors = ['red', 'blue', 'green', 'purple', 'orange']\n",
    "\n",
    "for i in range(n_clusters):\n",
    "    cluster_points = X_pca[cluster_labels == i]\n",
    "    plt.scatter(cluster_points[:, 0], cluster_points[:, 1], \n",
    "               c=colors[i], label=f'簇 {i}', alpha=0.6, s=50)\n",
    "\n",
    "# 标记聚类中心\n",
    "centers_pca = pca.transform(kmeans.cluster_centers_)\n",
    "plt.scatter(centers_pca[:, 0], centers_pca[:, 1], \n",
    "           c='black', marker='x', s=200, linewidths=3, label='聚类中心')\n",
    "\n",
    "plt.xlabel(f'第一主成分 (解释方差: {pca.explained_variance_ratio_[0]:.3f})')\n",
    "plt.ylabel(f'第二主成分 (解释方差: {pca.explained_variance_ratio_[1]:.3f})')\n",
    "plt.title('K-means聚类结果 (PCA降维可视化)')\n",
    "plt.legend()\n",
    "plt.grid(True, alpha=0.3)\n",
    "plt.tight_layout()\n",
    "plt.savefig('聚类结果可视化.png', dpi=300, bbox_inches='tight')\n",
    "plt.show()\n",
    "\n",
    "print(f\"PCA累计解释方差: {sum(pca.explained_variance_ratio_):.3f}\")\n",
    "print(\"聚类结果可视化图已保存为 '聚类结果可视化.png'\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# 分析每个簇的特征词\n",
    "print(\"\\n分析各簇特征词...\")\n",
    "feature_names = tfidf_model.get_feature_names_out()\n",
    "\n",
    "for i in range(n_clusters):\n",
    "    print(f\"\\n簇 {i} 的特征词:\")\n",
    "    \n",
    "    # 获取该簇的样本\n",
    "    cluster_mask = cluster_labels == i\n",
    "    cluster_tfidf = X[cluster_mask]\n",
    "    \n",
    "    # 计算该簇的平均TF-IDF\n",
    "    mean_tfidf = np.mean(cluster_tfidf.toarray(), axis=0)\n",
    "    \n",
    "    # 获取top词汇\n",
    "    top_indices = mean_tfidf.argsort()[-10:][::-1]\n",
    "    top_words = [(feature_names[idx], mean_tfidf[idx]) for idx in top_indices]\n",
    "    \n",
    "    for word, score in top_words:\n",
    "        print(f\"  {word}: {score:.4f}\")\n",
    "    \n",
    "    # 显示该簇的示例评论\n",
    "    cluster_reviews = valid_data[valid_data['cluster'] == i]['评论内容'].head(3)\n",
    "    print(f\"  示例评论:\")\n",
    "    for j, review in enumerate(cluster_reviews, 1):\n",
    "        print(f\"    {j}. {review[:100]}...\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# LDA主题建模\n",
    "print(\"\\n开始LDA主题建模...\")\n",
    "\n",
    "# 使用CountVectorizer为LDA准备数据\n",
    "count_vectorizer = CountVectorizer(max_features=1000, ngram_range=(1, 2))\n",
    "count_matrix = count_vectorizer.fit_transform(valid_data['processed_text'])\n",
    "\n",
    "# 训练LDA模型\n",
    "n_topics = 4\n",
    "lda = LatentDirichletAllocation(\n",
    "    n_components=n_topics,\n",
    "    random_state=42,\n",
    "    max_iter=10,\n",
    "    learning_method='online'\n",
    ")\n",
    "\n",
    "lda.fit(count_matrix)\n",
    "\n",
    "print(f\"LDA模型训练完成，共{n_topics}个主题\")\n",
    "print(f\"模型困惑度: {lda.perplexity(count_matrix):.2f}\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# 显示每个主题的Top10关键词\n",
    "def display_topics(model, feature_names, no_top_words):\n",
    "    topics = {}\n",
    "    for topic_idx, topic in enumerate(model.components_):\n",
    "        top_words_idx = topic.argsort()[-no_top_words:][::-1]\n",
    "        top_words = [feature_names[i] for i in top_words_idx]\n",
    "        topics[f'主题{topic_idx}'] = top_words\n",
    "        print(f\"主题 {topic_idx}: {', '.join(top_words)}\")\n",
    "    return topics\n",
    "\n",
    "print(\"\\nLDA主题分析结果:\")\n",
    "count_feature_names = count_vectorizer.get_feature_names_out()\n",
    "topics = display_topics(lda, count_feature_names, 10)"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# 生成pyLDAvis交互式可视化\n",
    "try:\n",
    "    print(\"\\n生成LDA交互式可视化...\")\n",
    "    pyLDAvis.enable_notebook()\n",
    "    vis = pyLDAvis.sklearn.prepare(lda, count_matrix, count_vectorizer)\n",
    "    \n",
    "    # 保存为HTML文件\n",
    "    pyLDAvis.save_html(vis, 'LDA主题分布可视化.html')\n",
    "    print(\"LDA交互式可视化已保存为 'LDA主题分布可视化.html'\")\n",
    "    \n",
    "    # 在notebook中显示\n",
    "    pyLDAvis.display(vis)\n",
    "    \n",
    "except Exception as e:\n",
    "    print(f\"生成LDA可视化时出错: {e}\")\n",
    "    print(\"将生成静态的主题分布图...\")\n",
    "    \n",
    "    # 备选方案：生成静态主题分布图\n",
    "    doc_topic_dist = lda.transform(count_matrix)\n",
    "    \n",
    "    plt.figure(figsize=(12, 8))\n",
    "    for i in range(n_topics):\n",
    "        plt.hist(doc_topic_dist[:, i], bins=20, alpha=0.7, label=f'主题 {i}')\n",
    "    \n",
    "    plt.xlabel('主题概率')\n",
    "    plt.ylabel('文档数量')\n",
    "    plt.title('文档-主题分布')\n",
    "    plt.legend()\n",
    "    plt.tight_layout()\n",
    "    plt.savefig('主题分布图.png', dpi=300, bbox_inches='tight')\n",
    "    plt.show()\n",
    "    \n",
    "    print(\"主题分布图已保存为 '主题分布图.png'\")"
   ]
  },

  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 6. 分析总结与报告生成"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# 生成分析报告\n",
    "def generate_analysis_report():\n",
    "    \"\"\"生成完整的分析报告\"\"\"\n",
    "    \n",
    "    report = f\"\"\"\n",
    "# 《解忧杂货铺》书籍评论多模态文本分析报告\n",
    "\n",
    "## 数据概览\n",
    "- **数据集大小**: {len(df)} 条评论\n",
    "- **有效数据**: {len(valid_data)} 条评论\n",
    "- **平均评论长度**: {df['评论内容'].str.len().mean():.1f} 字符\n",
    "- **评分分布**: {dict(df['用户评分'].value_counts())}\n",
    "\n",
    "## 情感分析结果\n",
    "- **平均情感评分**: {df['sentiment_score'].mean():.3f} (0-1区间，越接近1越积极)\n",
    "- **情感分布**:\n",
    "  - 积极评论: {(df['sentiment_label'] == '积极').sum()} 条 ({(df['sentiment_label'] == '积极').sum()/len(df)*100:.1f}%)\n",
    "  - 中性评论: {(df['sentiment_label'] == '中性').sum()} 条 ({(df['sentiment_label'] == '中性').sum()/len(df)*100:.1f}%)\n",
    "  - 消极评论: {(df['sentiment_label'] == '消极').sum()} 条 ({(df['sentiment_label'] == '消极').sum()/len(df)*100:.1f}%)\n",
    "\n",
    "## 高频词分析\n",
    "- **总词汇数**: {len(all_words)}\n",
    "- **唯一词汇数**: {len(word_freq)}\n",
    "- **Top 10 高频词**: {[word for word, freq in word_freq.most_common(10)]}\n",
    "\n",
    "## 机器学习模型性能\n",
    "\"\"\"\n",
    "    \n",
    "    # 添加模型性能\n",
    "    if results:\n",
    "        for name, result in results.items():\n",
    "            report += f\"- **{name}**: 准确率 {result['accuracy']:.3f}, F1-score {result['f1_score']:.3f}\\n\"\n",
    "    \n",
    "    report += f\"\"\"\n",
    "\n",
    "## 文本聚类结果\n",
    "- **聚类数量**: {n_clusters}\n",
    "- **聚类分布**: {dict(pd.Series(cluster_labels).value_counts().sort_index())}\n",
    "- **PCA解释方差**: {sum(pca.explained_variance_ratio_):.3f}\n",
    "\n",
    "## LDA主题建模\n",
    "- **主题数量**: {n_topics}\n",
    "- **模型困惑度**: {lda.perplexity(count_matrix):.2f}\n",
    "\"\"\"\n",
    "    \n",
    "    # 添加主题关键词\n",
    "    if 'topics' in locals():\n",
    "        for topic_name, keywords in topics.items():\n",
    "            report += f\"- **{topic_name}**: {', '.join(keywords[:5])}\\n\"\n",
    "    \n",
    "    report += f\"\"\"\n",
    "\n",
    "## 分析完成\n",
    "\"\"\"\n",
    "\n",
    "## 核心发现\n",
    "1. **整体情感倾向**: 读者对《解忧杂货铺》的评价以积极为主，平均情感评分为 {df['sentiment_score'].mean():.3f}\n",
    "2. **高频关键词**: 反映了读者对书籍的温暖、治愈等正面感受\n",
    "3. **文本聚类**: 评论可分为 {n_clusters} 个主要类别，体现了不同读者群体的关注点\n",
    "4. **主题分布**: LDA模型识别出 {n_topics} 个主要讨论主题\n",
    "5. **机器学习模型**: 多种算法在情感分类任务上表现良好\n",
    "\n",
    "## 技术路径说明\n",
    "1. **数据预处理**: 使用jieba分词，自定义词典，去除停用词\n",
    "2. **情感分析**: 基于SnowNLP的情感评分模型\n",
    "3. **特征工程**: TF-IDF向量化，max_features=2000\n",
    "4. **机器学习**: 决策树、朴素贝叶斯、SVM三种分类算法\n",
    "5. **聚类分析**: K-means聚类 + PCA降维可视化\n",
    "6. **主题建模**: LDA主题模型 + pyLDAvis可视化\n",
    "\n",
    "\n",
    "---\n",
    "*报告生成时间: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}*\n",
    "\"\"\"\n",
    "    \n",
    "    return report\n",
    "\n",
    "# 生成并保存报告\n",
    "analysis_report = generate_analysis_report()\n",
    "print(\"=\" * 80)\n",
    "print(\"分析报告预览:\")\n",
    "print(\"=\" * 80)\n",
    "print(analysis_report[:1000] + \"...\")\n",
    "\n",
    "# 保存完整报告\n",
    "with open('解忧杂货铺文本分析报告.md', 'w', encoding='utf-8') as f:\n",
    "    f.write(analysis_report)\n",
    "\n",
    "print(\"\\n完整分析报告已保存为 '解忧杂货铺文本分析报告.md'\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# 生成文件清单\n",
    "output_files = [\n",
    "    '情感分析结果.png',\n",
    "    '词云图.png', \n",
    "    '核心关键词频次.png',\n",
    "    '模型性能对比.png',\n",
    "    '混淆矩阵热力图.png',\n",
    "    '聚类结果可视化.png',\n",
    "    '主题分布图.png',\n",
    "    'LDA主题分布可视化.html',\n",

    "    '解忧杂货铺文本分析报告.md',\n",
    "    '解忧杂货铺文本分析.ipynb'\n",
    "]\n",
    "\n",
    "print(\"\\n=\" * 60)\n",
    "print(\"项目输出文件清单:\")\n",
    "print(\"=\" * 60)\n",
    "\n",
    "for i, file in enumerate(output_files, 1):\n",
    "    print(f\"{i:2d}. {file}\")\n",
    "\n",
    "print(\"\\n=\" * 60)\n",
    "print(\"《解忧杂货铺》书籍评论多模态文本分析项目完成！\")\n",
    "print(\"=\" * 60)\n",
    "\n",
    "# 显示项目统计信息\n",
    "print(f\"\\n项目统计:\")\n",
    "print(f\"- 处理评论数量: {len(valid_data)}\")\n",
    "print(f\"- 生成图表数量: {len([f for f in output_files if f.endswith('.png')])}\")\n",
    "print(f\"- 机器学习模型: {len(results) if 'results' in locals() else 0}\")\n",
    "print(f\"- 聚类簇数: {n_clusters}\")\n",
    "print(f\"- LDA主题数: {n_topics}\")\n",

   ]
  }
 ],
 "metadata": {
  "kernelspec": {
   "display_name": "Python 3",
   "language": "python",
   "name": "python3"
  },
  "language_info": {
   "codemirror_mode": {
    "name": "ipython",
    "version": 3
   },
   "file_extension": ".py",
   "mimetype": "text/x-python",
   "name": "python",
   "nbconvert_exporter": "python",
   "pygments_lexer": "ipython3",
   "version": "3.8.5"
  }
 },
 "nbformat": 4,
 "nbformat_minor": 4
}
