# 《解忧杂货铺》书籍评论多模态文本分析

## 项目概述

本项目对《解忧杂货铺》书籍评论数据进行全面的多模态文本分析，包括情感分析、词云生成、预测建模、文本聚类、主题建模和社交网络分析。

## 技术栈

- **文本处理**: jieba分词、SnowNLP情感分析
- **机器学习**: scikit-learn (决策树、朴素贝叶斯、SVM)
- **可视化**: matplotlib、seaborn、wordcloud
- **主题建模**: LDA + pyLDAvis交互式可视化
- **网络分析**: NetworkX社交网络分析
- **数据处理**: pandas、numpy

## 分析内容

### 1. 数据预处理
- 文本清洗：去除标点、特殊符号、停用词
- 自定义词典：集成书籍专有名词
- 分词工具：jieba精确模式

### 2. 情感倾向分析
- 基于SnowNLP的情感评分（0-1连续值）
- 情感分布直方图和占比饼图

### 3. 高频词与词云分析
- TF-IDF权重计算（Top 50词汇）
- 书本形状词云图生成
- 核心关键词频次统计

### 4. 预测建模
- 决策树分类（max_depth=5, criterion='entropy'）
- 朴素贝叶斯（alpha=1.0）
- SVM（kernel='linear', C=1）
- 评估指标：准确率、F1-score、混淆矩阵

### 5. 文本聚类与LDA
- K-means聚类（n_clusters=3）
- PCA降维2D可视化
- LDA主题模型（num_topics=4）
- pyLDAvis交互式主题分布图



## 文件结构

```
├── 解忧杂货铺书籍评论数据.csv          # 原始数据文件
├── 解忧杂货铺文本分析.ipynb            # 主要分析代码
├── requirements.txt                    # 依赖包列表
├── README.md                          # 项目说明文档
└── 输出文件/
    ├── 情感分析结果.png               # 情感分布图表
    ├── 词云图.png                     # 书本形状词云
    ├── 核心关键词频次.png             # 关键词频次图
    ├── 模型性能对比.png               # ML模型性能对比
    ├── 混淆矩阵热力图.png             # 模型混淆矩阵
    ├── 聚类结果可视化.png             # K-means聚类结果
    ├── 主题分布图.png                 # LDA主题分布
    ├── LDA主题分布可视化.html         # 交互式主题可视化

    └── 解忧杂货铺文本分析报告.md      # 完整分析报告
```

## 安装与运行

### 1. 环境准备
```bash
# 安装依赖包
pip install -r requirements.txt
```

### 2. 运行分析
```bash
# 启动Jupyter Notebook
jupyter notebook

# 打开并运行 解忧杂货铺文本分析.ipynb
```

### 3. 查看结果
- 所有图表将保存为PNG格式（300dpi）
- 分析报告保存为Markdown格式
- LDA可视化保存为HTML交互式文件

## 主要发现

1. **情感倾向**: 读者对《解忧杂货铺》评价以积极为主
2. **核心主题**: 温暖、治愈、人生选择等关键词频繁出现
3. **读者群体**: 可分为3个主要类别，体现不同关注点

5. **预测模型**: 多种机器学习算法的情感分类效果

## 技术特色

- **多模态分析**: 结合统计分析、机器学习、文本挖掘
- **可视化丰富**: 10+种图表类型，300dpi高清输出
- **交互式展示**: pyLDAvis主题建模可视化
- **完整流程**: 从数据预处理到报告生成的端到端分析

## 注意事项

1. 确保数据文件编码为gb18030
2. 中文字体路径可能需要根据系统调整
3. 大数据集建议调整采样参数以提高运行效率


## 作者

本项目基于《解忧杂货铺》书籍评论数据，采用多种先进的文本分析技术，为文学作品的读者反馈分析提供了完整的技术方案。
