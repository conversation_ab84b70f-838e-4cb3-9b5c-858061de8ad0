# 《解忧杂货铺》书籍评论文本分析项目 - 简化版

## 项目完成情况

✅ **已完成的功能模块**：

### 1. 数据预处理
- 自动安装依赖包
- 文本清洗和分词（jieba）
- 自定义词典集成
- 停用词过滤

### 2. 情感分析
- SnowNLP情感评分
- 情感分布可视化
- 正负中性情感分类

### 3. 词频分析与词云
- TF-IDF权重计算
- 高频词统计
- 书本形状词云生成
- 核心关键词分析

### 4. 机器学习建模
- 决策树分类器
- 朴素贝叶斯分类器
- SVM支持向量机
- 模型性能对比
- 混淆矩阵可视化

### 5. 文本聚类
- K-means聚类分析
- PCA降维可视化
- 聚类特征词分析

### 6. 主题建模
- LDA主题模型
- 主题关键词提取
- 主题分布可视化

## 已删除的复杂功能

❌ **观点挖掘与社交网络分析**（已移除）
- 原因：代码复杂度高，依赖包多
- 简化后更易运行和维护

## 输出文件清单

📁 **生成的分析结果**：
1. `情感分析结果.png` - 情感分布图表
2. `词云图.png` - 书本形状词云
3. `核心关键词频次.png` - 关键词频次图
4. `模型性能对比.png` - ML模型性能对比
5. `混淆矩阵热力图.png` - 模型混淆矩阵
6. `聚类结果可视化.png` - K-means聚类结果
7. `主题分布图.png` - LDA主题分布
8. `LDA主题分布可视化.html` - 交互式主题可视化
9. `解忧杂货铺文本分析报告.md` - 完整分析报告
10. `解忧杂货铺文本分析.ipynb` - Jupyter分析代码

## 技术栈（简化版）

### 核心库
- **数据处理**: pandas, numpy
- **文本分析**: jieba, snownlp
- **机器学习**: scikit-learn
- **可视化**: matplotlib, seaborn, wordcloud

### 分析方法
1. **文本预处理**: 分词 + 停用词过滤
2. **情感分析**: SnowNLP情感评分
3. **特征工程**: TF-IDF向量化
4. **机器学习**: 3种分类算法对比
5. **聚类分析**: K-means + PCA可视化
6. **主题建模**: LDA主题提取

## 运行说明

### 1. 环境准备
```bash
# 运行测试脚本（自动安装依赖）
python test_analysis.py
```

### 2. 执行分析
```bash
# 启动Jupyter Notebook
jupyter notebook

# 运行 解忧杂货铺文本分析.ipynb
```

### 3. 查看结果
- 所有图表自动保存为PNG格式（300dpi）
- 分析报告保存为Markdown格式
- 可选的LDA交互式可视化

## 项目特色

🎯 **简化优势**：
- **易于运行**: 自动安装依赖，一键启动
- **代码精简**: 去除复杂网络分析，专注核心功能
- **结果丰富**: 10种可视化图表 + 详细报告
- **技术全面**: 涵盖情感分析、机器学习、聚类、主题建模

🔧 **技术亮点**：
- 自定义中文分词词典
- 多种机器学习算法对比
- 书本形状创意词云
- PCA降维聚类可视化
- LDA主题建模分析

## 适用场景

📚 **应用领域**：
- 文学作品评论分析
- 产品评价情感挖掘
- 社交媒体文本分析
- 客户反馈分析
- 学术研究项目

## 注意事项

⚠️ **使用提醒**：
1. 确保数据文件编码为gb18030
2. 中文字体路径可能需要根据系统调整
3. 大数据集建议调整采样参数以提高运行效率

---

**项目状态**: ✅ 完成并测试通过  
**最后更新**: 2024年  
**技术支持**: 基于Python生态的完整文本分析解决方案
